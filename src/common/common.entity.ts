import { InputType, ObjectType, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, Virtual } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class MongooseSchema {
  /** MongoDB ObjectId */
  _id: Types.ObjectId;

  @Virtual({
    get(this: MongooseSchema) {
      return this._id.toString();
    },
  })
  /** String representation of the MongoDB ObjectId */
  id: string;

  /** Document creation timestamp */
  createdAt: Date;

  /** Document last update timestamp */
  updatedAt: Date;
}

@ObjectType()
export class Coordinates {
  /** Latitude */
  latitude: number;

  /** Longitude */
  longitude: number;
}

@ObjectType()
export class Address {
  /** Address */
  @Prop({ required: true })
  address: string;

  /** Coordinates */
  @Prop({ required: true, type: Object })
  coordinates: Coordinates;

  /** Metro line */
  @Prop()
  metroLine?: string;

  /** Metro station */
  @Prop()
  metroStation?: string;
}

export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
registerEnumType(DayOfWeek, { name: 'DayOfWeek' });

/**Inputs */
@InputType()
export class CoordinatesInput {
  /** Latitude */
  latitude: number;

  /** Longitude */
  longitude: number;
}

@InputType()
export class AddressInput {
  /** Address */
  address: string;

  /** Coordinates */
  coordinates: CoordinatesInput;

  /** Metro line */
  metroLine?: string;

  /** Metro station */
  metroStation?: string;
}

@InputType()
export class DayTimingInput {
  /** Day of the week */
  day: DayOfWeek;

  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc. */
  timings: number[];
}
