import {
  Field,
  ID,
  InputType,
  ObjectType,
  registerEnumType,
} from '@nestjs/graphql';
import { Prop, Schema, Virtual } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class MongooseSchema {
  @Field(() => ID)
  _id: Types.ObjectId;

  @Virtual({
    get(this: MongooseSchema) {
      return this._id.toString();
    },
  })
  @Field(() => ID)
  id: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}

@ObjectType()
export class Coordinates {
  @Field(() => Number, { description: 'Latitude' })
  latitude: number;

  @Field(() => Number, { description: 'Longitude' })
  longitude: number;
}

@ObjectType()
export class Address {
  @Field(() => String, { description: 'address' })
  @Prop({ required: true })
  address: string;

  @Field(() => Coordinates, { description: 'Coordinates' })
  @Prop({ required: true, type: Object })
  coordinates: Coordinates;

  @Field(() => String, { nullable: true, description: 'Metro line' })
  @Prop()
  metroLine?: string;

  // optional metro station
  @Field(() => String, { nullable: true, description: 'Metro station' })
  @Prop()
  metroStation?: string;
}

export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
registerEnumType(DayOfWeek, { name: 'DayOfWeek' });

/**Inputs */
@InputType()
export class CoordinatesInput {
  @Field(() => Number, { description: 'Latitude' })
  latitude: number;

  @Field(() => Number, { description: 'Longitude' })
  longitude: number;
}

@InputType()
export class AddressInput {
  @Field(() => String, { description: 'Address' })
  address: string;

  @Field(() => CoordinatesInput, { description: 'Coordinates' })
  coordinates: CoordinatesInput;

  @Field(() => String, { nullable: true, description: 'Metro line' })
  metroLine?: string;

  @Field(() => String, { nullable: true, description: 'Metro station' })
  metroStation?: string;
}

@InputType()
export class DayTimingInput {
  @Field(() => DayOfWeek, { description: 'Day of the week' })
  day: DayOfWeek;

  @Field(() => [Number], {
    description:
      'Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc.',
  })
  timings: number[];
}
