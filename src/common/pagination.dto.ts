import { Field, InputType, ObjectType, Int } from '@nestjs/graphql';
import { IsOptional, IsPositive, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { fromPairs } from 'lodash';

@InputType()
export class SortConfigInput {
  @Field(() => String, { description: 'Field to sort by' })
  field: string;

  @Field(() => String, {
    description: 'Sort order: "asc" or "desc"',
    defaultValue: 'desc',
  })
  order: 'asc' | 'desc' = 'desc';
}

@InputType()
export class PaginationInput {
  @Field(() => Int, { nullable: true, description: 'Page number (1-based)' })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Min(1)
  page: number = 1;

  @Field(() => Int, { nullable: true, description: 'Number of items per page' })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => Math.min(value as number, 100))
  limit: number = 30;

  @Field(() => [SortConfigInput], {
    nullable: true,
    description: 'Sort configuration array',
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return fromPairs(
        value.map((config: SortConfigInput) => [config.field, config.order]),
      );
    }
    return { createdAt: 'desc' };
  })
  sortConfig: Record<string, 'asc' | 'desc'> = { createdAt: 'desc' };
}

// Generic paginated response type factory
export function createPaginatedType<T>(ItemType: new () => T) {
  @ObjectType({ isAbstract: true })
  abstract class PaginatedType {
    @Field(() => [ItemType], { description: 'Array of documents' })
    docs: T[];

    @Field(() => Int, { description: 'Total number of documents' })
    totalDocs: number;

    @Field(() => Int, { description: 'Number of documents per page' })
    limit: number;

    @Field(() => Int, { description: 'Current page number' })
    page: number;

    @Field(() => Int, { description: 'Total number of pages' })
    totalPages: number;

    @Field(() => Int, { nullable: true, description: 'Previous page number' })
    prevPage?: number;

    @Field(() => Int, { nullable: true, description: 'Next page number' })
    nextPage?: number;

    @Field(() => Boolean, { description: 'Whether there is a previous page' })
    hasPrevPage: boolean;

    @Field(() => Boolean, { description: 'Whether there is a next page' })
    hasNextPage: boolean;

    @Field(() => Int, {
      nullable: true,
      description: 'Starting index of documents on current page',
    })
    pagingCounter?: number;
  }

  return PaginatedType;
}
