import { ObjectType } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.dto';

@ObjectType()
@Schema()
export class ClubCategory extends MongooseSchema {
  /** Club Category name */
  @Prop({ required: true, index: true, unique: true })
  name: string;

  /** Club Category description */
  @Prop({ required: false })
  description: string;
}

@ObjectType()
export class PaginatedClubCategories extends createPaginatedType(
  ClubCategory,
) {}

export const ClubCategorySchema = SchemaFactory.createForClass(ClubCategory);
