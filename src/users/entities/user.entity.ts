import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.dto';

export enum UserRoles {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

registerEnumType(UserRoles, { name: 'UserRoles' });
registerEnumType(UserStatus, { name: 'UserStatus' });

@ObjectType()
export class Contact {
  @Field(() => String, { description: 'Country code (e.g., +1, +91)' })
  countryCode: string;

  @Field(() => String, { description: 'Phone number' })
  phone: string;
}

@ObjectType()
@Schema()
export class User extends MongooseSchema {
  @Field(() => String, { description: 'user fullname' })
  @Prop({ required: true })
  fullname: string;

  @Field(() => String, { description: "user's email" })
  @Prop({ required: true, unique: true })
  email: string;

  @Field(() => UserStatus, { description: 'user active status' })
  @Prop({ required: true, default: UserStatus.ACTIVE, enum: UserStatus })
  userStatus: UserStatus;

  @Field(() => UserRoles, { description: 'user role' })
  @Prop({ required: true, enum: Object.values(UserRoles) })
  role: UserRoles;

  @Prop({ required: true })
  password: string;

  @Field(() => Contact, { description: 'User contact information' })
  @Prop({ required: true, type: Object })
  contact: Contact;

  @Field(() => Boolean, {
    description: 'Whether user has accepted terms and conditions',
  })
  @Prop({ required: true })
  acceptedTermsAndConditions: boolean;

  @Field(() => Boolean, {
    description:
      'Whether user wants to receive discounts, royalty offers and updates',
  })
  @Prop({ required: true })
  subscribeToUpdates: boolean;
}

@ObjectType()
export class PaginatedUsers extends createPaginatedType(User) {}

export const UserSchema = SchemaFactory.createForClass(User);
