import { ConflictException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as argon from 'argon2';
import { FilterQuery, PaginateModel } from 'mongoose';
import { PaginationInput } from 'src/common/pagination.dto';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name)
    private user: PaginateModel<User>,
  ) {}

  async create(createUserInput: CreateUserInput) {
    // Check if user exists with same email
    const userExists = await this.user.findOne({
      email: createUserInput.email,
    });

    if (userExists) throw new ConflictException('user already exists');

    return this.user.create(createUserInput);
  }

  async findAll(
    filter: FilterQuery<User> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.user.paginate(filter, paginationInput);
  }

  findOne(filter: FilterQuery<User>) {
    return this.user.findOne(filter);
  }

  async update(id: string, updateUserInput: UpdateUserInput) {
    if (updateUserInput.password)
      updateUserInput.password = await argon.hash(updateUserInput.password);
    return this.user.findByIdAndUpdate(id, updateUserInput, { new: true });
  }
}
