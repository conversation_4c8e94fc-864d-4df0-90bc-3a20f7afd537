import { InputType, Field } from '@nestjs/graphql';
import { UserRoles, UserStatus } from '../entities/user.entity';
import {
  IsString,
  IsEnum,
  MinLength,
  IsEmail,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

@InputType()
export class ContactInput {
  @Field(() => String, { description: 'Country code (e.g., +1, +91)' })
  @IsString()
  countryCode: string;

  @Field(() => String, { description: 'Phone number' })
  @IsString()
  phone: string;
}

@InputType()
export class CreateUserInput {
  @Field(() => String, { description: 'user fullname' })
  @IsString()
  fullname: string;

  @Field(() => String, { description: 'user phone number' })
  @IsEmail()
  @Transform(({ value }) => (value as string).toLowerCase())
  email: string;

  @Field(() => UserRoles, { description: 'user role' })
  @IsEnum(UserRoles)
  role: UserRoles;

  @Field(() => String, { description: 'user password' })
  @IsString()
  @MinLength(4)
  password: string;

  @Field(() => UserStatus, { description: 'user active status' })
  @IsEnum(UserStatus)
  userStatus: UserStatus;

  @Field(() => ContactInput, { description: 'User contact information' })
  @ValidateNested()
  @Type(() => ContactInput)
  contact: ContactInput;

  @Field(() => Boolean, {
    description: 'Whether user has accepted terms and conditions',
  })
  @IsBoolean()
  acceptedTermsAndConditions: boolean;

  @Field(() => Boolean, {
    description:
      'Whether user wants to receive discounts, royalty offers and updates',
  })
  @IsBoolean()
  subscribeToUpdates: boolean;
}
