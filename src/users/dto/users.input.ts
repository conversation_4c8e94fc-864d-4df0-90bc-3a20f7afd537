import { Field, InputType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { PaginationInput } from 'src/common/pagination.dto';

@InputType()
export class UsersInput extends PaginationInput {
  @Field(() => PaginationInput, {
    nullable: true,
    description: 'Pagination options',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}
