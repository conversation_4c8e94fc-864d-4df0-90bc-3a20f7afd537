import { Field, InputType } from '@nestjs/graphql';
import { Transform } from 'class-transformer';
import { IsEmail, IsString, MinLength } from 'class-validator';

@InputType()
export class SignInInput {
  @IsEmail()
  @Field(() => String, { description: 'user phone number' })
  @Transform(({ value }) => (value as string).toLowerCase())
  email: string;

  @IsString()
  @MinLength(4)
  @Field(() => String, { description: 'user password' })
  password: string;
}
